/**
 * BrushTool 单元测试
 */
import { shallowMount } from '@vue/test-utils';
import BrushTool from '@/components/ui/BrushTool.vue';
import {
  localVue,
  defaultMountOptions,
  createMockAdapter,
  waitForNextTick
} from '../../test-utils.js';

// Mock 适配器
const mockAdapter = {
  adapterType: 'fabric',
  isInitialized: true,

  // 绘制相关方法
  enableDrawingMode: jest.fn(),
  disableDrawingMode: jest.fn(),
  startDrawing: jest.fn(),
  stopDrawing: jest.fn(),
  setBrushColor: jest.fn(),
  setBrushSize: jest.fn(),
  setBrushOpacity: jest.fn(),
  clearCanvas: jest.fn(),

  // 路径和笔触管理
  addPath: jest.fn().mockResolvedValue('path-id-123'),
  addLine: jest.fn().mockResolvedValue('line-id-123'),
  addDrawingPath: jest.fn().mockResolvedValue('drawing-path-id-123'),
  addStroke: jest.fn(),
  removeStroke: jest.fn(),
  updateStroke: jest.fn(),

  // 状态管理
  getState: jest.fn(() => ({})),
  setState: jest.fn(),

  // 事件系统
  on: jest.fn(),
  off: jest.fn(),
  emit: jest.fn(),

  // 兼容性检查
  checkCompatibility: jest.fn(() => ({
    isSupported: true,
    supportedFeatures: ['drawing', 'brushSettings'],
    unsupportedFeatures: []
  })),

  // 性能指标
  getPerformanceMetrics: jest.fn(() => ({
    operationCount: 0,
    renderTime: 0
  }))
};

// Mock Canvas API
const mockCanvas = {
  getContext: jest.fn().mockReturnValue({
    clearRect: jest.fn(),
    drawImage: jest.fn(),
    beginPath: jest.fn(),
    moveTo: jest.fn(),
    lineTo: jest.fn(),
    stroke: jest.fn(),
    arc: jest.fn(),
    fill: jest.fn(),
    globalCompositeOperation: 'source-over',
    strokeStyle: '#000000',
    lineWidth: 1,
    lineCap: 'round',
    lineJoin: 'round',
    globalAlpha: 1
  }),
  getBoundingClientRect: jest.fn().mockReturnValue({
    left: 0,
    top: 0,
    width: 800,
    height: 600
  }),
  width: 800,
  height: 600
};

global.document.createElement = jest.fn().mockImplementation((tagName) => {
  if (tagName === 'canvas') {
    return mockCanvas;
  }
  return {
    style: {},
    appendChild: jest.fn(),
    remove: jest.fn()
  };
});

describe('BrushTool', () => {
  let wrapper;

  beforeEach(() => {
    wrapper = shallowMount(BrushTool, {
      localVue,
      propsData: {
        adapter: mockAdapter,
        adapterType: 'fabric',
        width: 800,
        height: 600
      },
      stubs: {
        'color-picker': {
          name: 'ColorPicker',
          props: ['value', 'disabled'],
          template: '<div class="mock-color-picker" @click="$emit(\'change\', value)"></div>'
        }
      },
      attachTo: document.body
    });

    // 重置所有mock
    jest.clearAllMocks();
  });

  afterEach(() => {
    if (wrapper) {
      wrapper.destroy();
    }
  });

  describe('适配器集成', () => {
    test('应该正确验证适配器prop', () => {
      expect(wrapper.props('adapter')).toBe(mockAdapter);
      expect(wrapper.props('adapterType')).toBe('fabric');
    });

    test('应该在开始绘制时启用适配器绘制模式', async () => {
      await wrapper.vm.startDrawing(100, 100, 1);

      expect(mockAdapter.enableDrawingMode).toHaveBeenCalledWith({
        width: wrapper.vm.brushSize,
        color: wrapper.vm.brushColor,
        opacity: wrapper.vm.brushOpacity / 100
      });
    });

    test('应该在完成绘制时同步笔触到适配器', async () => {
      // 开始绘制
      await wrapper.vm.startDrawing(100, 100, 1);
      
      // 添加一些点
      wrapper.vm.continueDrawing(110, 110, 1);
      wrapper.vm.continueDrawing(120, 120, 1);
      
      // 完成绘制
      await wrapper.vm.finishDrawing();

      expect(mockAdapter.addPath).toHaveBeenCalled();
    });

    test('应该正确处理橡皮擦模式', async () => {
      wrapper.vm.isEraserMode = true;
      
      await wrapper.vm.startDrawing(100, 100, 1);

      expect(mockAdapter.enableDrawingMode).toHaveBeenCalledWith({
        width: wrapper.vm.brushSize,
        color: 'transparent',
        opacity: wrapper.vm.brushOpacity / 100
      });
    });
  });

  describe('适配器类型特定功能', () => {
    test('应该正确处理Fabric适配器', async () => {
      await wrapper.vm.startDrawing(100, 100, 1);
      wrapper.vm.continueDrawing(110, 110, 1);
      await wrapper.vm.finishDrawing();

      expect(mockAdapter.addPath).toHaveBeenCalledWith(
        expect.stringContaining('M 100 100'),
        expect.objectContaining({
          stroke: wrapper.vm.brushColor,
          strokeWidth: wrapper.vm.brushSize,
          opacity: wrapper.vm.brushOpacity / 100,
          fill: 'transparent'
        })
      );
    });

    test('应该正确处理Konva适配器', async () => {
      await wrapper.setProps({ adapterType: 'konva' });

      await wrapper.vm.startDrawing(100, 100, 1);
      wrapper.vm.continueDrawing(110, 110, 1);
      await wrapper.vm.finishDrawing();

      expect(mockAdapter.addLine).toHaveBeenCalledWith(
        [100, 100, 110, 110],
        expect.objectContaining({
          stroke: wrapper.vm.brushColor,
          strokeWidth: wrapper.vm.brushSize,
          opacity: wrapper.vm.brushOpacity / 100,
          lineCap: 'round',
          lineJoin: 'round'
        })
      );
    });

    test('应该正确处理TUI适配器', async () => {
      await wrapper.setProps({ adapterType: 'tui' });

      await wrapper.vm.startDrawing(100, 100, 1);
      wrapper.vm.continueDrawing(110, 110, 1);
      await wrapper.vm.finishDrawing();

      expect(mockAdapter.addDrawingPath).toHaveBeenCalledWith(
        expect.arrayContaining([
          { x: 100, y: 100, pressure: 1 },
          { x: 110, y: 110, pressure: 1 }
        ]),
        expect.objectContaining({
          width: wrapper.vm.brushSize,
          color: wrapper.vm.brushColor
        })
      );
    });
  });

  describe('SVG路径转换', () => {
    test('应该正确将点转换为SVG路径', () => {
      const points = [
        { x: 100, y: 100 },
        { x: 110, y: 110 },
        { x: 120, y: 120 }
      ];

      const result = wrapper.vm._convertPointsToSVGPath(points);

      expect(result).toBe('M 100 100 L 110 110 L 120 120');
    });

    test('应该处理空点数组', () => {
      const result = wrapper.vm._convertPointsToSVGPath([]);
      expect(result).toBe('');
    });

    test('应该处理单个点', () => {
      const points = [{ x: 100, y: 100 }];
      const result = wrapper.vm._convertPointsToSVGPath(points);
      expect(result).toBe('M 100 100');
    });
  });

  describe('绘制状态管理', () => {
    test('应该正确管理绘制状态', async () => {
      expect(wrapper.vm.isDrawing).toBe(false);

      await wrapper.vm.startDrawing(100, 100, 1);
      expect(wrapper.vm.isDrawing).toBe(true);

      await wrapper.vm.finishDrawing();
      expect(wrapper.vm.isDrawing).toBe(false);
    });

    test('应该正确创建笔触对象', async () => {
      wrapper.vm.selectedBrushType = 'pencil';
      wrapper.vm.brushColor = '#ff0000';
      wrapper.vm.brushSize = 15;
      wrapper.vm.brushOpacity = 80;

      await wrapper.vm.startDrawing(100, 100, 1);

      expect(wrapper.vm.currentStroke).toEqual({
        type: 'pencil',
        color: '#ff0000',
        size: 15,
        opacity: 0.8,
        hardness: expect.any(Number),
        flow: expect.any(Number),
        spacing: expect.any(Number),
        blendMode: 'normal',
        isEraser: false,
        points: [{ x: 100, y: 100, pressure: 1 }]
      });
    });

    test('应该正确处理橡皮擦笔触', async () => {
      wrapper.vm.isEraserMode = true;

      await wrapper.vm.startDrawing(100, 100, 1);

      expect(wrapper.vm.currentStroke.color).toBe('transparent');
      expect(wrapper.vm.currentStroke.blendMode).toBe('destination-out');
      expect(wrapper.vm.currentStroke.isEraser).toBe(true);
    });
  });

  describe('错误处理', () => {
    test('应该处理适配器启用绘制模式失败', async () => {
      mockAdapter.enableDrawingMode.mockImplementationOnce(() => {
        throw new Error('Enable drawing mode failed');
      });

      // 不应该抛出错误，应该被捕获
      await expect(wrapper.vm.startDrawing(100, 100, 1)).resolves.toBeUndefined();
    });

    test('应该处理适配器同步笔触失败', async () => {
      mockAdapter.addPath.mockRejectedValueOnce(new Error('Sync stroke failed'));

      await wrapper.vm.startDrawing(100, 100, 1);
      await wrapper.vm.finishDrawing();

      expect(wrapper.emitted('error')).toBeTruthy();
      expect(wrapper.emitted('error')[0][0]).toEqual({
        type: 'stroke-sync-failed',
        message: '同步笔触到适配器失败',
        error: expect.any(Error)
      });
    });
  });

  describe('适配器兼容性', () => {
    test('应该处理不支持绘制方法的适配器', async () => {
      const incompatibleAdapter = {};
      await wrapper.setProps({ adapter: incompatibleAdapter });

      // 不应该抛出错误
      await expect(wrapper.vm._enableAdapterDrawingMode()).resolves.toBeUndefined();
    });

    test('应该处理部分支持的适配器', async () => {
      const partialAdapter = {
        enableDrawingMode: jest.fn()
        // 缺少其他方法
      };
      await wrapper.setProps({ adapter: partialAdapter });

      await wrapper.vm.startDrawing(100, 100, 1);
      await wrapper.vm.finishDrawing();

      expect(partialAdapter.enableDrawingMode).toHaveBeenCalled();
      // 不应该抛出错误，即使缺少同步方法
    });
  });

  describe('画笔设置', () => {
    test('应该正确更新画笔设置', () => {
      wrapper.vm.selectBrushType('marker');
      expect(wrapper.vm.selectedBrushType).toBe('marker');

      wrapper.vm.brushColor = '#00ff00';
      wrapper.vm.brushSize = 20;
      wrapper.vm.brushOpacity = 50;

      expect(wrapper.vm.brushColor).toBe('#00ff00');
      expect(wrapper.vm.brushSize).toBe(20);
      expect(wrapper.vm.brushOpacity).toBe(50);
    });

    test('应该正确切换橡皮擦模式', () => {
      expect(wrapper.vm.isEraserMode).toBe(false);

      wrapper.vm.toggleEraserMode();
      expect(wrapper.vm.isEraserMode).toBe(true);

      wrapper.vm.toggleEraserMode();
      expect(wrapper.vm.isEraserMode).toBe(false);
    });
  });
});
