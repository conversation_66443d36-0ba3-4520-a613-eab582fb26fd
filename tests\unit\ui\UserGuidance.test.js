/**
 * UserGuidance 单元测试
 */
import { shallowMount } from '@vue/test-utils';
import UserGuidance from '@/components/ui/UserGuidance.vue';
import { errorHandler } from '@/utils/ErrorHandler.js';

// Mock errorHandler
jest.mock('@/utils/ErrorHandler.js', () => ({
  errorHandler: {
    onUserGuidance: jest.fn(),
    onContextualHelp: jest.fn(),
    getOperationGuidance: jest.fn(),
    getTroubleshootingSteps: jest.fn()
  }
}));

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  clear: jest.fn()
};
global.localStorage = localStorageMock;

describe('UserGuidance', () => {
  let wrapper;

  beforeEach(() => {
    wrapper = shallowMount(UserGuidance);
    jest.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
  });

  afterEach(() => {
    if (wrapper) {
      wrapper.destroy();
    }
  });

  describe('初始化', () => {
    test('应该正确渲染组件', () => {
      expect(wrapper.exists()).toBe(true);
      expect(wrapper.find('.user-guidance-container').exists()).toBe(true);
    });

    test('应该加载用户偏好设置', () => {
      const mockPreferences = JSON.stringify({
        showOnboarding: false,
        showContextTips: false,
        completedOnboarding: true
      });

      localStorageMock.getItem.mockReturnValue(mockPreferences);

      const newWrapper = shallowMount(UserGuidance);

      expect(newWrapper.vm.userPreferences.showOnboarding).toBe(false);
      expect(newWrapper.vm.userPreferences.showContextTips).toBe(false);
      expect(newWrapper.vm.userPreferences.completedOnboarding).toBe(true);

      newWrapper.destroy();
    });

    test('应该设置指导处理器', () => {
      expect(errorHandler.onUserGuidance).toHaveBeenCalled();
      expect(errorHandler.onContextualHelp).toHaveBeenCalled();
    });
  });

  describe('新手引导', () => {
    test('应该显示新手引导', (done) => {
      wrapper.vm.userPreferences.showOnboarding = true;
      wrapper.vm.userPreferences.completedOnboarding = false;

      // 模拟mounted中的setTimeout
      setTimeout(() => {
        wrapper.vm.showOnboarding = true;
        wrapper.vm.$nextTick(() => {
          expect(wrapper.vm.showOnboarding).toBe(true);
          done();
        });
      }, 100);
    });

    test('应该正确导航引导步骤', () => {
      wrapper.vm.showOnboarding = true;
      wrapper.vm.currentOnboardingStep = 0;

      // 下一步
      wrapper.vm.nextOnboardingStep();
      expect(wrapper.vm.currentOnboardingStep).toBe(1);

      // 上一步
      wrapper.vm.previousOnboardingStep();
      expect(wrapper.vm.currentOnboardingStep).toBe(0);

      // 不能再往前
      wrapper.vm.previousOnboardingStep();
      expect(wrapper.vm.currentOnboardingStep).toBe(0);
    });

    test('应该跳过引导', () => {
      wrapper.vm.showOnboarding = true;
      wrapper.vm.skipOnboarding();

      expect(wrapper.vm.showOnboarding).toBe(false);
      expect(wrapper.vm.userPreferences.showOnboarding).toBe(false);
      expect(localStorageMock.setItem).toHaveBeenCalled();
    });

    test('应该完成引导', () => {
      wrapper.vm.showOnboarding = true;
      wrapper.vm.finishOnboarding();

      expect(wrapper.vm.showOnboarding).toBe(false);
      expect(wrapper.vm.userPreferences.completedOnboarding).toBe(true);
      expect(wrapper.emitted('onboarding-completed')).toBeTruthy();
      expect(localStorageMock.setItem).toHaveBeenCalled();
    });

    test('应该重新开始引导', () => {
      wrapper.vm.currentOnboardingStep = 2;
      wrapper.vm.showQuickHelp = true;

      wrapper.vm.restartOnboarding();

      expect(wrapper.vm.currentOnboardingStep).toBe(0);
      expect(wrapper.vm.showOnboarding).toBe(true);
      expect(wrapper.vm.showQuickHelp).toBe(false);
    });

    test('应该获取高亮样式', () => {
      // Mock DOM element
      const mockElement = {
        getBoundingClientRect: () => ({
          top: 100,
          left: 200,
          width: 150,
          height: 50
        })
      };

      document.getElementById = jest.fn(() => mockElement);

      const style = wrapper.vm.getHighlightStyle('test-element');

      expect(style.position).toBe('fixed');
      expect(style.top).toBe('96px'); // 100 - 4
      expect(style.left).toBe('196px'); // 200 - 4
      expect(style.width).toBe('158px'); // 150 + 8
      expect(style.height).toBe('58px'); // 50 + 8
    });

    test('应该处理不存在的元素', () => {
      document.getElementById = jest.fn(() => null);
      document.querySelector = jest.fn(() => null);

      const style = wrapper.vm.getHighlightStyle('non-existent');

      expect(style).toEqual({});
    });
  });

  describe('上下文提示', () => {
    test('应该显示上下文提示', (done) => {
      wrapper.vm.userPreferences.showContextTips = true;

      const mockElement = {
        getBoundingClientRect: () => ({
          top: 100,
          left: 200,
          width: 150,
          height: 50
        })
      };

      const tip = {
        message: '这是一个提示',
        position: 'bottom'
      };

      wrapper.vm.showContextTip(tip, mockElement);

      expect(wrapper.vm.showContextTip).toBe(true);
      expect(wrapper.vm.contextTip).toEqual(tip);

      // 测试自动隐藏
      setTimeout(() => {
        expect(wrapper.vm.showContextTip).toBe(false);
        done();
      }, 5100);
    });

    test('应该在禁用时不显示提示', () => {
      wrapper.vm.userPreferences.showContextTips = false;

      const tip = { message: '测试提示' };
      const mockElement = { getBoundingClientRect: () => ({}) };

      wrapper.vm.showContextTip(tip, mockElement);

      expect(wrapper.vm.showContextTip).toBe(false);
    });

    test('应该手动隐藏提示', () => {
      wrapper.vm.showContextTip = true;
      wrapper.vm.contextTip = { message: '测试' };

      wrapper.vm.hideContextTip();

      expect(wrapper.vm.showContextTip).toBe(false);
      expect(wrapper.vm.contextTip).toBeNull();
    });

    test('应该正确计算提示位置', () => {
      const mockElement = {
        getBoundingClientRect: () => ({
          top: 100,
          left: 200,
          width: 150,
          height: 50,
          bottom: 150
        })
      };

      // Mock window dimensions
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 1024
      });

      wrapper.vm.contextTip = { position: 'bottom' };

      const style = wrapper.vm.calculateTipPosition(mockElement);

      expect(style.position).toBe('fixed');
      expect(parseInt(style.top)).toBeGreaterThan(0);
      expect(parseInt(style.left)).toBeGreaterThan(0);
      expect(style.width).toBe('250px');
    });

    test('应该处理边界情况', () => {
      const mockElement = {
        getBoundingClientRect: () => ({
          top: 5, // 接近顶部
          left: 5, // 接近左边
          width: 150,
          height: 50,
          bottom: 55
        })
      };

      wrapper.vm.contextTip = {};

      const style = wrapper.vm.calculateTipPosition(mockElement);

      expect(wrapper.vm.contextTip.position).toBe('top'); // 应该显示在下方
      expect(parseInt(style.left)).toBe(10); // 应该调整到边界内
    });
  });

  describe('快速帮助', () => {
    test('应该切换快速帮助面板', () => {
      expect(wrapper.vm.showQuickHelp).toBe(false);

      wrapper.vm.toggleQuickHelp();
      expect(wrapper.vm.showQuickHelp).toBe(true);

      wrapper.vm.toggleQuickHelp();
      expect(wrapper.vm.showQuickHelp).toBe(false);
    });

    test('应该显示操作指导', () => {
      const mockGuidance = {
        title: '文件上传指南',
        steps: ['选择文件', '上传'],
        tips: ['支持拖拽']
      };

      errorHandler.getOperationGuidance.mockReturnValue(mockGuidance);

      wrapper.vm.showOperationGuidance('file_upload');

      expect(errorHandler.getOperationGuidance).toHaveBeenCalledWith('file_upload');
      expect(wrapper.emitted('show-guidance')).toBeTruthy();
      expect(wrapper.emitted('show-guidance')[0][0]).toEqual(mockGuidance);
      expect(wrapper.vm.showQuickHelp).toBe(false);
    });

    test('应该显示故障排除', () => {
      const mockTroubleshooting = {
        title: '网络问题排除',
        steps: [
          { step: '检查连接', description: '检查网络', action: 'check_connection' }
        ]
      };

      errorHandler.getTroubleshootingSteps.mockReturnValue(mockTroubleshooting);

      wrapper.vm.showTroubleshooting('network_issues');

      expect(errorHandler.getTroubleshootingSteps).toHaveBeenCalledWith('network_issues');
      expect(wrapper.emitted('show-troubleshooting')).toBeTruthy();
      expect(wrapper.emitted('show-troubleshooting')[0][0]).toEqual(mockTroubleshooting);
      expect(wrapper.vm.showQuickHelp).toBe(false);
    });

    test('应该联系技术支持', () => {
      wrapper.vm.contactSupport();

      expect(wrapper.emitted('contact-support')).toBeTruthy();
      expect(wrapper.vm.showQuickHelp).toBe(false);
    });
  });

  describe('用户偏好设置', () => {
    test('应该保存用户偏好设置', () => {
      wrapper.vm.userPreferences.showOnboarding = false;
      wrapper.vm.userPreferences.completedOnboarding = true;

      wrapper.vm.saveUserPreferences();

      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'photo-editor-user-preferences',
        JSON.stringify(wrapper.vm.userPreferences)
      );
    });

    test('应该处理无效的存储数据', () => {
      localStorageMock.getItem.mockReturnValue('invalid json');

      const newWrapper = shallowMount(UserGuidance);

      // 应该使用默认值
      expect(newWrapper.vm.userPreferences.showOnboarding).toBe(true);
      expect(newWrapper.vm.userPreferences.completedOnboarding).toBe(false);

      newWrapper.destroy();
    });
  });

  describe('事件处理', () => {
    test('应该处理指导事件', () => {
      const guidanceCallback = errorHandler.onUserGuidance.mock.calls[0][0];
      const mockGuidance = { title: '测试指导' };
      const mockContext = { operation: 'test' };

      guidanceCallback(mockGuidance, mockContext);

      expect(wrapper.emitted('show-guidance')).toBeTruthy();
      expect(wrapper.emitted('show-guidance')[0]).toEqual([mockGuidance, mockContext]);
    });

    test('应该处理上下文帮助事件', () => {
      const helpCallback = errorHandler.onContextualHelp.mock.calls[0][0];
      const mockTroubleshooting = { title: '测试排除' };
      const mockContext = { issue: 'test' };

      helpCallback(mockTroubleshooting, mockContext);

      expect(wrapper.emitted('show-troubleshooting')).toBeTruthy();
      expect(wrapper.emitted('show-troubleshooting')[0]).toEqual([mockTroubleshooting, mockContext]);
    });
  });

  describe('响应式行为', () => {
    test('应该在移动设备上正确显示', () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375
      });

      const mobileWrapper = shallowMount(UserGuidance);

      expect(mobileWrapper.exists()).toBe(true);
      // 在实际应用中，这里会检查CSS类或样式的变化

      mobileWrapper.destroy();
    });
  });
});
